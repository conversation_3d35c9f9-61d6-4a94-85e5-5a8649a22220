{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": ["**/_fresh/*"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUnusedVariables": "error"}, "suspicious": {"noExplicitAny": "error"}, "style": {"useConst": "error", "useTemplate": "error"}}}, "javascript": {"formatter": {"quoteStyle": "double", "trailingComma": "es5", "semicolons": "always"}}}