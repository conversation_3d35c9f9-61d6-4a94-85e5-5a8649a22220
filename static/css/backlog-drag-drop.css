/* Estilos para drag & drop en el Product Backlog */
.backlog-item {
  transition: transform 0.2s, box-shadow 0.2s, opacity 0.2s;
}

.backlog-item:hover {
  cursor: grab;
}

.backlog-item:active {
  cursor: grabbing;
}

.backlog-item-dragging {
  transform: scale(1.02);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  opacity: 0.8;
}

.drop-zone {
  transition: background-color 0.2s, border 0.2s;
}

.drop-zone-highlight {
  background-color: rgba(59, 130, 246, 0.1);
  border: 2px dashed #3b82f6;
}

/* Estilos para las columnas de prioridad */
.priority-critical.drop-zone-highlight {
  background-color: rgba(239, 68, 68, 0.2);
  border: 2px dashed #ef4444;
}

.priority-high.drop-zone-highlight {
  background-color: rgba(249, 115, 22, 0.2);
  border: 2px dashed #f97316;
}

.priority-medium.drop-zone-highlight {
  background-color: rgba(245, 158, 11, 0.2);
  border: 2px dashed #f59e0b;
}

.priority-low.drop-zone-highlight {
  background-color: rgba(16, 185, 129, 0.2);
  border: 2px dashed #10b981;
}

/* Estilos para las etiquetas de puntos */
.story-points {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  padding: 0 8px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.75rem;
}

.story-points-1 {
  background-color: #e0f2fe;
  color: #0369a1;
}

.story-points-2 {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.story-points-3 {
  background-color: #e0e7ff;
  color: #4338ca;
}

.story-points-5 {
  background-color: #ede9fe;
  color: #6d28d9;
}

.story-points-8 {
  background-color: #fae8ff;
  color: #a21caf;
}

.story-points-13 {
  background-color: #fce7f3;
  color: #be185d;
}

.story-points-21 {
  background-color: #ffe4e6;
  color: #be123c;
}

.story-points-default {
  background-color: #f3f4f6;
  color: #4b5563;
}
