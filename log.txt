[{"resource": "/c:/Users/<USER>/TGZ/WorkflowS/routes/projects/[id]/metrics.tsx", "owner": "_generated_diagnostic_collection_name_#6", "code": {"value": "lint/style/useImportType", "target": {"$mid": 1, "path": "/linter/rules/use-import-type", "scheme": "https", "authority": "biomejs.dev"}}, "severity": 8, "message": "All these imports are only used as types.", "source": "biome", "startLineNumber": 11, "startColumn": 8, "endLineNumber": 11, "endColumn": 51}, {"resource": "/c:/Users/<USER>/TGZ/WorkflowS/routes/projects/[id]/metrics.tsx", "owner": "_generated_diagnostic_collection_name_#6", "code": {"value": "lint/correctness/noUnusedVariables", "target": {"$mid": 1, "path": "/linter/rules/no-unused-variables", "scheme": "https", "authority": "biomejs.dev"}}, "severity": 8, "message": "This parameter is unused.", "source": "biome", "startLineNumber": 485, "startColumn": 57, "endLineNumber": 485, "endColumn": 62}, {"resource": "/c:/Users/<USER>/TGZ/WorkflowS/routes/projects/[id]/metrics.tsx", "owner": "_generated_diagnostic_collection_name_#6", "code": {"value": "lint/style/useSelfClosingElements", "target": {"$mid": 1, "path": "/linter/rules/use-self-closing-elements", "scheme": "https", "authority": "biomejs.dev"}}, "severity": 8, "message": "JSX elements without children should be marked as self-closing. In JSX, it is valid for any element to be self-closing.", "source": "biome", "startLineNumber": 503, "startColumn": 27, "endLineNumber": 506, "endColumn": 34}, {"resource": "/c:/Users/<USER>/TGZ/WorkflowS/routes/projects/[id]/metrics.tsx", "owner": "_generated_diagnostic_collection_name_#6", "code": {"value": "lint/style/useSelfClosingElements", "target": {"$mid": 1, "path": "/linter/rules/use-self-closing-elements", "scheme": "https", "authority": "biomejs.dev"}}, "severity": 8, "message": "JSX elements without children should be marked as self-closing. In JSX, it is valid for any element to be self-closing.", "source": "biome", "startLineNumber": 543, "startColumn": 19, "endLineNumber": 546, "endColumn": 26}, {"resource": "/c:/Users/<USER>/TGZ/WorkflowS/routes/projects/[id]/metrics.tsx", "owner": "_generated_diagnostic_collection_name_#6", "code": {"value": "lint/style/useSelfClosingElements", "target": {"$mid": 1, "path": "/linter/rules/use-self-closing-elements", "scheme": "https", "authority": "biomejs.dev"}}, "severity": 8, "message": "JSX elements without children should be marked as self-closing. In JSX, it is valid for any element to be self-closing.", "source": "biome", "startLineNumber": 554, "startColumn": 19, "endLineNumber": 557, "endColumn": 26}, {"resource": "/c:/Users/<USER>/TGZ/WorkflowS/routes/projects/[id]/metrics.tsx", "owner": "_generated_diagnostic_collection_name_#6", "code": {"value": "lint/style/useSelfClosingElements", "target": {"$mid": 1, "path": "/linter/rules/use-self-closing-elements", "scheme": "https", "authority": "biomejs.dev"}}, "severity": 8, "message": "JSX elements without children should be marked as self-closing. In JSX, it is valid for any element to be self-closing.", "source": "biome", "startLineNumber": 565, "startColumn": 19, "endLineNumber": 568, "endColumn": 26}, {"resource": "/c:/Users/<USER>/TGZ/WorkflowS/routes/projects/[id]/metrics.tsx", "owner": "_generated_diagnostic_collection_name_#6", "code": {"value": "lint/style/useSelfClosingElements", "target": {"$mid": 1, "path": "/linter/rules/use-self-closing-elements", "scheme": "https", "authority": "biomejs.dev"}}, "severity": 8, "message": "JSX elements without children should be marked as self-closing. In JSX, it is valid for any element to be self-closing.", "source": "biome", "startLineNumber": 615, "startColumn": 23, "endLineNumber": 620, "endColumn": 30}, {"resource": "/c:/Users/<USER>/TGZ/WorkflowS/routes/projects/[id]/metrics.tsx", "owner": "deno", "code": "no-unused-vars", "severity": 4, "message": "`index` is never used\nIf this is intentional, prefix it with an underscore like `_index`", "source": "deno-lint", "startLineNumber": 485, "startColumn": 57, "endLineNumber": 485, "endColumn": 62}]