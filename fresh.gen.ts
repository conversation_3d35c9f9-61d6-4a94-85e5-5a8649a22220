// DO NOT EDIT. This file is generated by Fresh.
// This file SHOULD be checked into source version control.
// This file is automatically updated during development when running `dev.ts`.

import * as $_404 from "./routes/_404.tsx";
import * as $_app from "./routes/_app.tsx";
import * as $about from "./routes/about.tsx";
import * as $admin_index from "./routes/admin/index.tsx";
import * as $admin_users from "./routes/admin/users.tsx";
import * as $api_admin_users from "./routes/api/admin/users.ts";
import * as $api_admin_users_delete from "./routes/api/admin/users/delete.ts";
import * as $api_auth_session from "./routes/api/auth/session.tsx";
import * as $api_comments_taskId_ from "./routes/api/comments/[taskId].ts";
import * as $api_conversations_id_messages from "./routes/api/conversations/[id]/messages.ts";
import * as $api_conversations_index from "./routes/api/conversations/index.ts";
import * as $api_deliverables_id_ from "./routes/api/deliverables/[id].tsx";
import * as $api_deliverables_id_attachments from "./routes/api/deliverables/[id]/attachments.tsx";
import * as $api_deliverables_id_submit from "./routes/api/deliverables/[id]/submit.tsx";
import * as $api_deliverables_index from "./routes/api/deliverables/index.tsx";
import * as $api_evaluations_id_ from "./routes/api/evaluations/[id].tsx";
import * as $api_evaluations_id_finalize from "./routes/api/evaluations/[id]/finalize.tsx";
import * as $api_evaluations_index from "./routes/api/evaluations/index.tsx";
import * as $api_login from "./routes/api/login.ts";
import * as $api_logout from "./routes/api/logout.ts";
import * as $api_projects_id_health from "./routes/api/projects/[id]/health.ts";
import * as $api_projects_id_members from "./routes/api/projects/[id]/members.ts";
import * as $api_projects_id_metrics from "./routes/api/projects/[id]/metrics.ts";
import * as $api_projects_index from "./routes/api/projects/index.ts";
import * as $api_projects_members from "./routes/api/projects/members.ts";
import * as $api_projects_members_id_ from "./routes/api/projects/members/[id].ts";
import * as $api_register from "./routes/api/register.ts";
import * as $api_reports_id_export from "./routes/api/reports/[id]/export.ts";
import * as $api_reports_generate from "./routes/api/reports/generate.ts";
import * as $api_reports_schedule from "./routes/api/reports/schedule.ts";
import * as $api_rubrics_id_ from "./routes/api/rubrics/[id].tsx";
import * as $api_rubrics_id_duplicate from "./routes/api/rubrics/[id]/duplicate.tsx";
import * as $api_rubrics_index from "./routes/api/rubrics/index.tsx";
import * as $api_session from "./routes/api/session.ts";
import * as $api_sprints_id_ from "./routes/api/sprints/[id].ts";
import * as $api_sprints_id_burndown_debug from "./routes/api/sprints/[id]/burndown-debug.ts";
import * as $api_sprints_id_burndown from "./routes/api/sprints/[id]/burndown.ts";
import * as $api_sprints_id_metrics from "./routes/api/sprints/[id]/metrics.ts";
import * as $api_sprints_id_recalculate_burndown from "./routes/api/sprints/[id]/recalculate-burndown.ts";
import * as $api_sprints_id_user_stories from "./routes/api/sprints/[id]/user-stories.ts";
import * as $api_sprints_id_user_stories_userStoryId_ from "./routes/api/sprints/[id]/user-stories/[userStoryId].ts";
import * as $api_sprints_id_velocity from "./routes/api/sprints/[id]/velocity.ts";
import * as $api_sprints_index from "./routes/api/sprints/index.ts";
import * as $api_tasks_id_ from "./routes/api/tasks/[id].ts";
import * as $api_tasks_id_comments from "./routes/api/tasks/[id]/comments.ts";
import * as $api_tasks_id_history from "./routes/api/tasks/[id]/history.ts";
import * as $api_tasks_id_time from "./routes/api/tasks/[id]/time.ts";
import * as $api_tasks_index from "./routes/api/tasks/index.ts";
import * as $api_user_stories from "./routes/api/user-stories.ts";
import * as $api_user_stories_id_ from "./routes/api/user-stories/[id].ts";
import * as $api_users_id_ from "./routes/api/users/[id].ts";
import * as $api_users_id_metrics from "./routes/api/users/[id]/metrics.ts";
import * as $backlog_index from "./routes/backlog/index.tsx";
import * as $chat from "./routes/chat.tsx";
import * as $chat_disabled_index from "./routes/chat_disabled/index.tsx";
import * as $deliverables_id_evaluate from "./routes/deliverables/[id]/evaluate.tsx";
import * as $evaluations_id_ from "./routes/evaluations/[id].tsx";
import * as $evaluations_index from "./routes/evaluations/index.tsx";
import * as $index from "./routes/index.tsx";
import * as $login from "./routes/login.tsx";
import * as $logout from "./routes/logout.ts";
import * as $my_evaluations_index from "./routes/my-evaluations/index.tsx";
import * as $my_tasks from "./routes/my-tasks.tsx";
import * as $projects_id_ from "./routes/projects/[id].tsx";
import * as $projects_id_members from "./routes/projects/[id]/members.tsx";
import * as $projects_id_metrics from "./routes/projects/[id]/metrics.tsx";
import * as $projects_id_reports_generate from "./routes/projects/[id]/reports/generate.tsx";
import * as $projects_id_reports_index from "./routes/projects/[id]/reports/index.tsx";
import * as $projects_id_sprints from "./routes/projects/[id]/sprints.tsx";
import * as $projects_index from "./routes/projects/index.tsx";
import * as $register from "./routes/register.tsx";
import * as $reports_dashboard from "./routes/reports/dashboard.tsx";
import * as $reports_evaluations from "./routes/reports/evaluations.tsx";
import * as $reports_export from "./routes/reports/export.tsx";
import * as $reports_index from "./routes/reports/index.tsx";
import * as $reports_projects from "./routes/reports/projects.tsx";
import * as $reports_projects_id_ from "./routes/reports/projects/[id].tsx";
import * as $reports_sprints from "./routes/reports/sprints.tsx";
import * as $reports_users from "./routes/reports/users.tsx";
import * as $rubrics_id_ from "./routes/rubrics/[id].tsx";
import * as $rubrics_id_edit from "./routes/rubrics/[id]/edit.tsx";
import * as $rubrics_create from "./routes/rubrics/create.tsx";
import * as $rubrics_index from "./routes/rubrics/index.tsx";
import * as $rubrics_list from "./routes/rubrics/list.tsx";
import * as $sprints_id_ from "./routes/sprints/[id].tsx";
import * as $sprints_id_add_user_stories from "./routes/sprints/[id]/add-user-stories.tsx";
import * as $sprints_create from "./routes/sprints/create.tsx";
import * as $sprints_index from "./routes/sprints/index.tsx";
import * as $sprints_plan from "./routes/sprints/plan.tsx";
import * as $tasks_id_ from "./routes/tasks/[id].tsx";
import * as $test_sidebar from "./routes/test-sidebar.tsx";
import * as $unauthorized from "./routes/unauthorized.tsx";
import * as $user_stories_id_ from "./routes/user-stories/[id].tsx";
import * as $user_stories_id_assign_sprint from "./routes/user-stories/[id]/assign-sprint.tsx";
import * as $user_stories_id_tasks from "./routes/user-stories/[id]/tasks.tsx";
import * as $user_stories_edit_id_ from "./routes/user-stories/edit/[id].tsx";
import * as $user_stories_index from "./routes/user-stories/index.tsx";
import * as $welcome from "./routes/welcome.tsx";
import * as $AdminCreateUserForm from "./islands/AdminCreateUserForm.tsx";
import * as $AdminUsersList from "./islands/AdminUsersList.tsx";
import * as $AdminWelcomeOptions from "./islands/AdminWelcomeOptions.tsx";
import * as $AppShell from "./islands/AppShell.tsx";
import * as $AppShellExternal from "./islands/AppShellExternal.tsx";
import * as $AppSidebar from "./islands/AppSidebar.tsx";
import * as $AppSidebarExternal from "./islands/AppSidebarExternal.tsx";
import * as $AssignProjectForm from "./islands/AssignProjectForm.tsx";
import * as $Backlog_BacklogFilters from "./islands/Backlog/BacklogFilters.tsx";
import * as $Backlog_BacklogHeader from "./islands/Backlog/BacklogHeader.tsx";
import * as $Backlog_BacklogItemCard from "./islands/Backlog/BacklogItemCard.tsx";
import * as $Backlog_BacklogMetrics from "./islands/Backlog/BacklogMetrics.tsx";
import * as $Backlog_ProductBacklog from "./islands/Backlog/ProductBacklog.tsx";
import * as $Chat_ChatApp from "./islands/Chat/ChatApp.tsx";
import * as $Chat_ChatInterface from "./islands/Chat/ChatInterface.tsx";
import * as $Chat_ConversationList from "./islands/Chat/ConversationList.tsx";
import * as $Chat_NewConversationModal from "./islands/Chat/NewConversationModal.tsx";
import * as $CommonWelcomeOptions from "./islands/CommonWelcomeOptions.tsx";
import * as $CreateProjectForm from "./islands/CreateProjectForm.tsx";
import * as $DeleteProjectModal from "./islands/DeleteProjectModal.tsx";
import * as $DropdownMenu from "./islands/DropdownMenu.tsx";
import * as $EditProjectForm from "./islands/EditProjectForm.tsx";
import * as $EmptyProjectsMessage from "./islands/EmptyProjectsMessage.tsx";
import * as $Evaluations_DeliverableDetails from "./islands/Evaluations/DeliverableDetails.tsx";
import * as $Evaluations_EvaluationCard from "./islands/Evaluations/EvaluationCard.tsx";
import * as $Evaluations_EvaluationForm from "./islands/Evaluations/EvaluationForm.tsx";
import * as $Evaluations_EvaluationHistory from "./islands/Evaluations/EvaluationHistory.tsx";
import * as $Evaluations_EvaluationManager from "./islands/Evaluations/EvaluationManager.tsx";
import * as $Evaluations_EvaluationStats from "./islands/Evaluations/EvaluationStats.tsx";
import * as $Evaluations_EvaluationView from "./islands/Evaluations/EvaluationView.tsx";
import * as $Evaluations_PendingDeliverablesList from "./islands/Evaluations/PendingDeliverablesList.tsx";
import * as $Evaluations_RubricSelector from "./islands/Evaluations/RubricSelector.tsx";
import * as $Evaluations_StudentEvaluationsList from "./islands/Evaluations/StudentEvaluationsList.tsx";
import * as $HeaderMenu from "./islands/HeaderMenu.tsx";
import * as $HeaderNav from "./islands/HeaderNav.tsx";
import * as $LoginForm from "./islands/LoginForm.tsx";
import * as $LogoutButton from "./islands/LogoutButton.tsx";
import * as $Metrics_BurndownChart from "./islands/Metrics/BurndownChart.tsx";
import * as $Metrics_BurndownDebug from "./islands/Metrics/BurndownDebug.tsx";
import * as $Metrics_ProjectHealthGauge from "./islands/Metrics/ProjectHealthGauge.tsx";
import * as $Metrics_TeamVelocityChart from "./islands/Metrics/TeamVelocityChart.tsx";
import * as $Metrics_WorkDistributionChart from "./islands/Metrics/WorkDistributionChart.tsx";
import * as $MockupsIndex from "./islands/MockupsIndex.tsx";
import * as $Modal from "./islands/Modal.tsx";
import * as $NavFooter from "./islands/NavFooter.tsx";
import * as $NavFooterExternal from "./islands/NavFooterExternal.tsx";
import * as $NavMain from "./islands/NavMain.tsx";
import * as $NavUser from "./islands/NavUser.tsx";
import * as $ProductOwnerWelcomeOptions from "./islands/ProductOwnerWelcomeOptions.tsx";
import * as $ProjectCard from "./islands/ProjectCard.tsx";
import * as $ProjectModals_AssignProjectModal from "./islands/ProjectModals/AssignProjectModal.tsx";
import * as $ProjectModals_CreateProjectModal from "./islands/ProjectModals/CreateProjectModal.tsx";
import * as $ProjectModals_EditProjectModal from "./islands/ProjectModals/EditProjectModal.tsx";
import * as $Projects_ProjectMembersList from "./islands/Projects/ProjectMembersList.tsx";
import * as $ProjectsHeader from "./islands/ProjectsHeader.tsx";
import * as $ProjectsList from "./islands/ProjectsList.tsx";
import * as $ProjectsStatusBar from "./islands/ProjectsStatusBar.tsx";
import * as $RegisterForm from "./islands/RegisterForm.tsx";
import * as $Reports_ReportGenerator from "./islands/Reports/ReportGenerator.tsx";
import * as $Reports_ReportsList from "./islands/Reports/ReportsList.tsx";
import * as $Rubrics_DeleteRubricModal from "./islands/Rubrics/DeleteRubricModal.tsx";
import * as $Rubrics_DuplicateRubricForm from "./islands/Rubrics/DuplicateRubricForm.tsx";
import * as $Rubrics_RubricCreatePage from "./islands/Rubrics/RubricCreatePage.tsx";
import * as $Rubrics_RubricDetails from "./islands/Rubrics/RubricDetails.tsx";
import * as $Rubrics_RubricDetailsPage from "./islands/Rubrics/RubricDetailsPage.tsx";
import * as $Rubrics_RubricEditPage from "./islands/Rubrics/RubricEditPage.tsx";
import * as $Rubrics_RubricForm from "./islands/Rubrics/RubricForm.tsx";
import * as $Rubrics_RubricsList from "./islands/Rubrics/RubricsList.tsx";
import * as $Rubrics_RubricsManager from "./islands/Rubrics/RubricsManager.tsx";
import * as $ScrumMasterWelcomeOptions from "./islands/ScrumMasterWelcomeOptions.tsx";
import * as $SidebarProvider from "./islands/SidebarProvider.tsx";
import * as $Sprints_AddUserStoriesToSprint from "./islands/Sprints/AddUserStoriesToSprint.tsx";
import * as $Sprints_CreateSprintForm from "./islands/Sprints/CreateSprintForm.tsx";
import * as $Sprints_CreateSprintPage from "./islands/Sprints/CreateSprintPage.tsx";
import * as $Sprints_EditSprintForm from "./islands/Sprints/EditSprintForm.tsx";
import * as $Sprints_SprintCard from "./islands/Sprints/SprintCard.tsx";
import * as $Sprints_SprintPlanningPage from "./islands/Sprints/SprintPlanningPage.tsx";
import * as $Sprints_SprintsList from "./islands/Sprints/SprintsList.tsx";
import * as $Sprints_SprintsOverview from "./islands/Sprints/SprintsOverview.tsx";
import * as $Tasks_CreateTaskForm from "./islands/Tasks/CreateTaskForm.tsx";
import * as $Tasks_EditTaskForm from "./islands/Tasks/EditTaskForm.tsx";
import * as $Tasks_FilteredTasksList from "./islands/Tasks/FilteredTasksList.tsx";
import * as $Tasks_MyTasksList from "./islands/Tasks/MyTasksList.tsx";
import * as $Tasks_TaskCalendarView from "./islands/Tasks/TaskCalendarView.tsx";
import * as $Tasks_TaskCard from "./islands/Tasks/TaskCard.tsx";
import * as $Tasks_TaskComments from "./islands/Tasks/TaskComments.tsx";
import * as $Tasks_TaskDetailView from "./islands/Tasks/TaskDetailView.tsx";
import * as $Tasks_TaskEvaluation from "./islands/Tasks/TaskEvaluation.tsx";
import * as $Tasks_TaskFilters from "./islands/Tasks/TaskFilters.tsx";
import * as $Tasks_TaskGrouping from "./islands/Tasks/TaskGrouping.tsx";
import * as $Tasks_TaskHistory from "./islands/Tasks/TaskHistory.tsx";
import * as $Tasks_TaskListView from "./islands/Tasks/TaskListView.tsx";
import * as $Tasks_TaskViewSelector from "./islands/Tasks/TaskViewSelector.tsx";
import * as $Tasks_TasksList from "./islands/Tasks/TasksList.tsx";
import * as $Tasks_WorkloadExport from "./islands/Tasks/WorkloadExport.tsx";
import * as $Tasks_WorkloadMetrics from "./islands/Tasks/WorkloadMetrics.tsx";
import * as $Tasks_WorkloadPreferences from "./islands/Tasks/WorkloadPreferences.tsx";
import * as $Tasks_WorkloadSummary from "./islands/Tasks/WorkloadSummary.tsx";
import * as $TeamDeveloperWelcomeOptions from "./islands/TeamDeveloperWelcomeOptions.tsx";
import * as $UnauthorizedLogoutButton from "./islands/UnauthorizedLogoutButton.tsx";
import * as $UserInfoCard from "./islands/UserInfoCard.tsx";
import * as $UserStories_CreateUserStoryForm from "./islands/UserStories/CreateUserStoryForm.tsx";
import * as $UserStories_EditUserStoryForm from "./islands/UserStories/EditUserStoryForm.tsx";
import * as $UserStories_UserStoriesList from "./islands/UserStories/UserStoriesList.tsx";
import * as $UserStories_UserStoryCard from "./islands/UserStories/UserStoryCard.tsx";
import * as $WelcomeHeader from "./islands/WelcomeHeader.tsx";
import * as $WelcomeScreen from "./islands/WelcomeScreen.tsx";
import * as $welcome_AdminWelcomeOptions from "./islands/welcome/AdminWelcomeOptions.tsx";
import * as $welcome_CommonWelcomeOptions from "./islands/welcome/CommonWelcomeOptions.tsx";
import * as $welcome_InteractiveWelcomeCard from "./islands/welcome/InteractiveWelcomeCard.tsx";
import * as $welcome_WelcomeHeader from "./islands/welcome/WelcomeHeader.tsx";
import * as $welcome_WelcomeScreen from "./islands/welcome/WelcomeScreen.tsx";
import type { Manifest } from "$fresh/server.ts";

const manifest = {
  routes: {
    "./routes/_404.tsx": $_404,
    "./routes/_app.tsx": $_app,
    "./routes/about.tsx": $about,
    "./routes/admin/index.tsx": $admin_index,
    "./routes/admin/users.tsx": $admin_users,
    "./routes/api/admin/users.ts": $api_admin_users,
    "./routes/api/admin/users/delete.ts": $api_admin_users_delete,
    "./routes/api/auth/session.tsx": $api_auth_session,
    "./routes/api/comments/[taskId].ts": $api_comments_taskId_,
    "./routes/api/conversations/[id]/messages.ts":
      $api_conversations_id_messages,
    "./routes/api/conversations/index.ts": $api_conversations_index,
    "./routes/api/deliverables/[id].tsx": $api_deliverables_id_,
    "./routes/api/deliverables/[id]/attachments.tsx":
      $api_deliverables_id_attachments,
    "./routes/api/deliverables/[id]/submit.tsx": $api_deliverables_id_submit,
    "./routes/api/deliverables/index.tsx": $api_deliverables_index,
    "./routes/api/evaluations/[id].tsx": $api_evaluations_id_,
    "./routes/api/evaluations/[id]/finalize.tsx": $api_evaluations_id_finalize,
    "./routes/api/evaluations/index.tsx": $api_evaluations_index,
    "./routes/api/login.ts": $api_login,
    "./routes/api/logout.ts": $api_logout,
    "./routes/api/projects/[id]/health.ts": $api_projects_id_health,
    "./routes/api/projects/[id]/members.ts": $api_projects_id_members,
    "./routes/api/projects/[id]/metrics.ts": $api_projects_id_metrics,
    "./routes/api/projects/index.ts": $api_projects_index,
    "./routes/api/projects/members.ts": $api_projects_members,
    "./routes/api/projects/members/[id].ts": $api_projects_members_id_,
    "./routes/api/register.ts": $api_register,
    "./routes/api/reports/[id]/export.ts": $api_reports_id_export,
    "./routes/api/reports/generate.ts": $api_reports_generate,
    "./routes/api/reports/schedule.ts": $api_reports_schedule,
    "./routes/api/rubrics/[id].tsx": $api_rubrics_id_,
    "./routes/api/rubrics/[id]/duplicate.tsx": $api_rubrics_id_duplicate,
    "./routes/api/rubrics/index.tsx": $api_rubrics_index,
    "./routes/api/session.ts": $api_session,
    "./routes/api/sprints/[id].ts": $api_sprints_id_,
    "./routes/api/sprints/[id]/burndown-debug.ts":
      $api_sprints_id_burndown_debug,
    "./routes/api/sprints/[id]/burndown.ts": $api_sprints_id_burndown,
    "./routes/api/sprints/[id]/metrics.ts": $api_sprints_id_metrics,
    "./routes/api/sprints/[id]/recalculate-burndown.ts":
      $api_sprints_id_recalculate_burndown,
    "./routes/api/sprints/[id]/user-stories.ts": $api_sprints_id_user_stories,
    "./routes/api/sprints/[id]/user-stories/[userStoryId].ts":
      $api_sprints_id_user_stories_userStoryId_,
    "./routes/api/sprints/[id]/velocity.ts": $api_sprints_id_velocity,
    "./routes/api/sprints/index.ts": $api_sprints_index,
    "./routes/api/tasks/[id].ts": $api_tasks_id_,
    "./routes/api/tasks/[id]/comments.ts": $api_tasks_id_comments,
    "./routes/api/tasks/[id]/history.ts": $api_tasks_id_history,
    "./routes/api/tasks/[id]/time.ts": $api_tasks_id_time,
    "./routes/api/tasks/index.ts": $api_tasks_index,
    "./routes/api/user-stories.ts": $api_user_stories,
    "./routes/api/user-stories/[id].ts": $api_user_stories_id_,
    "./routes/api/users/[id].ts": $api_users_id_,
    "./routes/api/users/[id]/metrics.ts": $api_users_id_metrics,
    "./routes/backlog/index.tsx": $backlog_index,
    "./routes/chat.tsx": $chat,
    "./routes/chat_disabled/index.tsx": $chat_disabled_index,
    "./routes/deliverables/[id]/evaluate.tsx": $deliverables_id_evaluate,
    "./routes/evaluations/[id].tsx": $evaluations_id_,
    "./routes/evaluations/index.tsx": $evaluations_index,
    "./routes/index.tsx": $index,
    "./routes/login.tsx": $login,
    "./routes/logout.ts": $logout,
    "./routes/my-evaluations/index.tsx": $my_evaluations_index,
    "./routes/my-tasks.tsx": $my_tasks,
    "./routes/projects/[id].tsx": $projects_id_,
    "./routes/projects/[id]/members.tsx": $projects_id_members,
    "./routes/projects/[id]/metrics.tsx": $projects_id_metrics,
    "./routes/projects/[id]/reports/generate.tsx":
      $projects_id_reports_generate,
    "./routes/projects/[id]/reports/index.tsx": $projects_id_reports_index,
    "./routes/projects/[id]/sprints.tsx": $projects_id_sprints,
    "./routes/projects/index.tsx": $projects_index,
    "./routes/register.tsx": $register,
    "./routes/reports/dashboard.tsx": $reports_dashboard,
    "./routes/reports/evaluations.tsx": $reports_evaluations,
    "./routes/reports/export.tsx": $reports_export,
    "./routes/reports/index.tsx": $reports_index,
    "./routes/reports/projects.tsx": $reports_projects,
    "./routes/reports/projects/[id].tsx": $reports_projects_id_,
    "./routes/reports/sprints.tsx": $reports_sprints,
    "./routes/reports/users.tsx": $reports_users,
    "./routes/rubrics/[id].tsx": $rubrics_id_,
    "./routes/rubrics/[id]/edit.tsx": $rubrics_id_edit,
    "./routes/rubrics/create.tsx": $rubrics_create,
    "./routes/rubrics/index.tsx": $rubrics_index,
    "./routes/rubrics/list.tsx": $rubrics_list,
    "./routes/sprints/[id].tsx": $sprints_id_,
    "./routes/sprints/[id]/add-user-stories.tsx": $sprints_id_add_user_stories,
    "./routes/sprints/create.tsx": $sprints_create,
    "./routes/sprints/index.tsx": $sprints_index,
    "./routes/sprints/plan.tsx": $sprints_plan,
    "./routes/tasks/[id].tsx": $tasks_id_,
    "./routes/test-sidebar.tsx": $test_sidebar,
    "./routes/unauthorized.tsx": $unauthorized,
    "./routes/user-stories/[id].tsx": $user_stories_id_,
    "./routes/user-stories/[id]/assign-sprint.tsx":
      $user_stories_id_assign_sprint,
    "./routes/user-stories/[id]/tasks.tsx": $user_stories_id_tasks,
    "./routes/user-stories/edit/[id].tsx": $user_stories_edit_id_,
    "./routes/user-stories/index.tsx": $user_stories_index,
    "./routes/welcome.tsx": $welcome,
  },
  islands: {
    "./islands/AdminCreateUserForm.tsx": $AdminCreateUserForm,
    "./islands/AdminUsersList.tsx": $AdminUsersList,
    "./islands/AdminWelcomeOptions.tsx": $AdminWelcomeOptions,
    "./islands/AppShell.tsx": $AppShell,
    "./islands/AppShellExternal.tsx": $AppShellExternal,
    "./islands/AppSidebar.tsx": $AppSidebar,
    "./islands/AppSidebarExternal.tsx": $AppSidebarExternal,
    "./islands/AssignProjectForm.tsx": $AssignProjectForm,
    "./islands/Backlog/BacklogFilters.tsx": $Backlog_BacklogFilters,
    "./islands/Backlog/BacklogHeader.tsx": $Backlog_BacklogHeader,
    "./islands/Backlog/BacklogItemCard.tsx": $Backlog_BacklogItemCard,
    "./islands/Backlog/BacklogMetrics.tsx": $Backlog_BacklogMetrics,
    "./islands/Backlog/ProductBacklog.tsx": $Backlog_ProductBacklog,
    "./islands/Chat/ChatApp.tsx": $Chat_ChatApp,
    "./islands/Chat/ChatInterface.tsx": $Chat_ChatInterface,
    "./islands/Chat/ConversationList.tsx": $Chat_ConversationList,
    "./islands/Chat/NewConversationModal.tsx": $Chat_NewConversationModal,
    "./islands/CommonWelcomeOptions.tsx": $CommonWelcomeOptions,
    "./islands/CreateProjectForm.tsx": $CreateProjectForm,
    "./islands/DeleteProjectModal.tsx": $DeleteProjectModal,
    "./islands/DropdownMenu.tsx": $DropdownMenu,
    "./islands/EditProjectForm.tsx": $EditProjectForm,
    "./islands/EmptyProjectsMessage.tsx": $EmptyProjectsMessage,
    "./islands/Evaluations/DeliverableDetails.tsx":
      $Evaluations_DeliverableDetails,
    "./islands/Evaluations/EvaluationCard.tsx": $Evaluations_EvaluationCard,
    "./islands/Evaluations/EvaluationForm.tsx": $Evaluations_EvaluationForm,
    "./islands/Evaluations/EvaluationHistory.tsx":
      $Evaluations_EvaluationHistory,
    "./islands/Evaluations/EvaluationManager.tsx":
      $Evaluations_EvaluationManager,
    "./islands/Evaluations/EvaluationStats.tsx": $Evaluations_EvaluationStats,
    "./islands/Evaluations/EvaluationView.tsx": $Evaluations_EvaluationView,
    "./islands/Evaluations/PendingDeliverablesList.tsx":
      $Evaluations_PendingDeliverablesList,
    "./islands/Evaluations/RubricSelector.tsx": $Evaluations_RubricSelector,
    "./islands/Evaluations/StudentEvaluationsList.tsx":
      $Evaluations_StudentEvaluationsList,
    "./islands/HeaderMenu.tsx": $HeaderMenu,
    "./islands/HeaderNav.tsx": $HeaderNav,
    "./islands/LoginForm.tsx": $LoginForm,
    "./islands/LogoutButton.tsx": $LogoutButton,
    "./islands/Metrics/BurndownChart.tsx": $Metrics_BurndownChart,
    "./islands/Metrics/BurndownDebug.tsx": $Metrics_BurndownDebug,
    "./islands/Metrics/ProjectHealthGauge.tsx": $Metrics_ProjectHealthGauge,
    "./islands/Metrics/TeamVelocityChart.tsx": $Metrics_TeamVelocityChart,
    "./islands/Metrics/WorkDistributionChart.tsx":
      $Metrics_WorkDistributionChart,
    "./islands/MockupsIndex.tsx": $MockupsIndex,
    "./islands/Modal.tsx": $Modal,
    "./islands/NavFooter.tsx": $NavFooter,
    "./islands/NavFooterExternal.tsx": $NavFooterExternal,
    "./islands/NavMain.tsx": $NavMain,
    "./islands/NavUser.tsx": $NavUser,
    "./islands/ProductOwnerWelcomeOptions.tsx": $ProductOwnerWelcomeOptions,
    "./islands/ProjectCard.tsx": $ProjectCard,
    "./islands/ProjectModals/AssignProjectModal.tsx":
      $ProjectModals_AssignProjectModal,
    "./islands/ProjectModals/CreateProjectModal.tsx":
      $ProjectModals_CreateProjectModal,
    "./islands/ProjectModals/EditProjectModal.tsx":
      $ProjectModals_EditProjectModal,
    "./islands/Projects/ProjectMembersList.tsx": $Projects_ProjectMembersList,
    "./islands/ProjectsHeader.tsx": $ProjectsHeader,
    "./islands/ProjectsList.tsx": $ProjectsList,
    "./islands/ProjectsStatusBar.tsx": $ProjectsStatusBar,
    "./islands/RegisterForm.tsx": $RegisterForm,
    "./islands/Reports/ReportGenerator.tsx": $Reports_ReportGenerator,
    "./islands/Reports/ReportsList.tsx": $Reports_ReportsList,
    "./islands/Rubrics/DeleteRubricModal.tsx": $Rubrics_DeleteRubricModal,
    "./islands/Rubrics/DuplicateRubricForm.tsx": $Rubrics_DuplicateRubricForm,
    "./islands/Rubrics/RubricCreatePage.tsx": $Rubrics_RubricCreatePage,
    "./islands/Rubrics/RubricDetails.tsx": $Rubrics_RubricDetails,
    "./islands/Rubrics/RubricDetailsPage.tsx": $Rubrics_RubricDetailsPage,
    "./islands/Rubrics/RubricEditPage.tsx": $Rubrics_RubricEditPage,
    "./islands/Rubrics/RubricForm.tsx": $Rubrics_RubricForm,
    "./islands/Rubrics/RubricsList.tsx": $Rubrics_RubricsList,
    "./islands/Rubrics/RubricsManager.tsx": $Rubrics_RubricsManager,
    "./islands/ScrumMasterWelcomeOptions.tsx": $ScrumMasterWelcomeOptions,
    "./islands/SidebarProvider.tsx": $SidebarProvider,
    "./islands/Sprints/AddUserStoriesToSprint.tsx":
      $Sprints_AddUserStoriesToSprint,
    "./islands/Sprints/CreateSprintForm.tsx": $Sprints_CreateSprintForm,
    "./islands/Sprints/CreateSprintPage.tsx": $Sprints_CreateSprintPage,
    "./islands/Sprints/EditSprintForm.tsx": $Sprints_EditSprintForm,
    "./islands/Sprints/SprintCard.tsx": $Sprints_SprintCard,
    "./islands/Sprints/SprintPlanningPage.tsx": $Sprints_SprintPlanningPage,
    "./islands/Sprints/SprintsList.tsx": $Sprints_SprintsList,
    "./islands/Sprints/SprintsOverview.tsx": $Sprints_SprintsOverview,
    "./islands/Tasks/CreateTaskForm.tsx": $Tasks_CreateTaskForm,
    "./islands/Tasks/EditTaskForm.tsx": $Tasks_EditTaskForm,
    "./islands/Tasks/FilteredTasksList.tsx": $Tasks_FilteredTasksList,
    "./islands/Tasks/MyTasksList.tsx": $Tasks_MyTasksList,
    "./islands/Tasks/TaskCalendarView.tsx": $Tasks_TaskCalendarView,
    "./islands/Tasks/TaskCard.tsx": $Tasks_TaskCard,
    "./islands/Tasks/TaskComments.tsx": $Tasks_TaskComments,
    "./islands/Tasks/TaskDetailView.tsx": $Tasks_TaskDetailView,
    "./islands/Tasks/TaskEvaluation.tsx": $Tasks_TaskEvaluation,
    "./islands/Tasks/TaskFilters.tsx": $Tasks_TaskFilters,
    "./islands/Tasks/TaskGrouping.tsx": $Tasks_TaskGrouping,
    "./islands/Tasks/TaskHistory.tsx": $Tasks_TaskHistory,
    "./islands/Tasks/TaskListView.tsx": $Tasks_TaskListView,
    "./islands/Tasks/TaskViewSelector.tsx": $Tasks_TaskViewSelector,
    "./islands/Tasks/TasksList.tsx": $Tasks_TasksList,
    "./islands/Tasks/WorkloadExport.tsx": $Tasks_WorkloadExport,
    "./islands/Tasks/WorkloadMetrics.tsx": $Tasks_WorkloadMetrics,
    "./islands/Tasks/WorkloadPreferences.tsx": $Tasks_WorkloadPreferences,
    "./islands/Tasks/WorkloadSummary.tsx": $Tasks_WorkloadSummary,
    "./islands/TeamDeveloperWelcomeOptions.tsx": $TeamDeveloperWelcomeOptions,
    "./islands/UnauthorizedLogoutButton.tsx": $UnauthorizedLogoutButton,
    "./islands/UserInfoCard.tsx": $UserInfoCard,
    "./islands/UserStories/CreateUserStoryForm.tsx":
      $UserStories_CreateUserStoryForm,
    "./islands/UserStories/EditUserStoryForm.tsx":
      $UserStories_EditUserStoryForm,
    "./islands/UserStories/UserStoriesList.tsx": $UserStories_UserStoriesList,
    "./islands/UserStories/UserStoryCard.tsx": $UserStories_UserStoryCard,
    "./islands/WelcomeHeader.tsx": $WelcomeHeader,
    "./islands/WelcomeScreen.tsx": $WelcomeScreen,
    "./islands/welcome/AdminWelcomeOptions.tsx": $welcome_AdminWelcomeOptions,
    "./islands/welcome/CommonWelcomeOptions.tsx": $welcome_CommonWelcomeOptions,
    "./islands/welcome/InteractiveWelcomeCard.tsx":
      $welcome_InteractiveWelcomeCard,
    "./islands/welcome/WelcomeHeader.tsx": $welcome_WelcomeHeader,
    "./islands/welcome/WelcomeScreen.tsx": $welcome_WelcomeScreen,
  },
  baseUrl: import.meta.url,
} satisfies Manifest;

export default manifest;
