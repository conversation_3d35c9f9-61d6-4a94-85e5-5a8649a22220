name: Deploy
on:
  push:
    branches: dev
  pull_request:
    branches: dev

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest

    permissions:
      id-token: write # Needed for auth with Deno Deploy
      contents: read # Needed to clone the repository

    steps:
      - name: Clone repository
        uses: actions/checkout@v4

      - name: Install Deno
        uses: denoland/setup-deno@v2
        with:
          deno-version: v2.x

      - name: Build step
        run: "deno task build"

      - name: Upload to Deno Deploy
        uses: denoland/deployctl@v1
        with:
          project: "workflows-60"
          entrypoint: "main.ts"
          root: "."
          
          
